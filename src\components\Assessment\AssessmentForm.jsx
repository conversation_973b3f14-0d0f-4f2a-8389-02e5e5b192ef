import { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, Check, BookOpen } from 'lucide-react';
import AssessmentQuestion from './AssessmentQuestion';
import AssessmentSidebar from './AssessmentSidebar';
import MobileAssessmentNavbar from './MobileAssessmentNavbar';
import MobileBottomNavigation from './MobileBottomNavigation';
import MobilePhaseMenu from './MobilePhaseMenu';

const AssessmentForm = ({
  assessmentData,
  onSubmit,
  onNext,
  onPrevious,
  isLastAssessment = false,
  currentStep = 1,
  totalSteps = 3,
  isDebugMode = false,
  isProcessingSubmit = false,
  onFillAllAssessments,
  allAssessmentsFilled = false,
  prefilledAnswers = null, // New prop for prefilled answers from auto fill
  isAutoFillMode = false, // Flag indicating auto-fill mode
  onManualSubmit, // Manual submit function for auto-fill mode
  onNavigateToPhase, // New prop for phase navigation
  onClearStoredAnswers, // New prop to clear localStorage
}) => {
  const [answers, setAnswers] = useState({});
  const [currentPage, setCurrentPage] = useState(0);
  const [isPhaseMenuOpen, setIsPhaseMenuOpen] = useState(false);
  // Remove fixed questionsPerPage - we'll use category-based pagination

  // localStorage utility functions
  const getStorageKey = () => {
    const assessmentType = assessmentData.title.toLowerCase().replace(/\s+/g, '_');
    return `atma_assessment_answers_${assessmentType}`;
  };

  const saveAnswersToStorage = (answersToSave) => {
    try {
      const storageKey = getStorageKey();
      localStorage.setItem(storageKey, JSON.stringify(answersToSave));
    } catch (error) {
      console.warn('Failed to save answers to localStorage:', error);
    }
  };

  const loadAnswersFromStorage = () => {
    try {
      const storageKey = getStorageKey();
      const stored = localStorage.getItem(storageKey);
      return stored ? JSON.parse(stored) : {};
    } catch (error) {
      console.warn('Failed to load answers from localStorage:', error);
      return {};
    }
  };

  const clearAnswersFromStorage = () => {
    try {
      const storageKey = getStorageKey();
      localStorage.removeItem(storageKey);
    } catch (error) {
      console.warn('Failed to clear answers from localStorage:', error);
    }
  };

  // Get categories as an array for pagination
  const categories = Object.entries(assessmentData.categories);
  const totalPages = categories.length;

  // Get current category and its questions
  const currentCategory = categories[currentPage];
  const currentCategoryKey = currentCategory ? currentCategory[0] : null;
  const currentCategoryData = currentCategory ? currentCategory[1] : null;

  // Get all questions for current category
  const currentQuestions = [];
  if (currentCategoryData) {
    // Regular questions
    currentCategoryData.questions.forEach((question, index) => {
      currentQuestions.push({
        question,
        categoryKey: currentCategoryKey,
        questionKey: `${currentCategoryKey}_${index}`,
        isReverse: false
      });
    });

    // Reverse questions (for Big Five)
    if (currentCategoryData.reverseQuestions) {
      currentCategoryData.reverseQuestions.forEach((question, index) => {
        currentQuestions.push({
          question,
          categoryKey: currentCategoryKey,
          questionKey: `${currentCategoryKey}_reverse_${index}`,
          isReverse: true
        });
      });
    }
  }

  // Flatten all questions from all categories (for compatibility with existing functions)
  const allQuestions = [];
  Object.entries(assessmentData.categories).forEach(([categoryKey, category]) => {
    // Regular questions
    category.questions.forEach((question, index) => {
      allQuestions.push({
        question,
        categoryKey,
        questionKey: `${categoryKey}_${index}`,
        isReverse: false
      });
    });

    // Reverse questions (for Big Five)
    if (category.reverseQuestions) {
      category.reverseQuestions.forEach((question, index) => {
        allQuestions.push({
          question,
          categoryKey,
          questionKey: `${categoryKey}_reverse_${index}`,
          isReverse: true
        });
      });
    }
  });

  // Load answers from localStorage when component first mounts
  useEffect(() => {
    // Load from localStorage when component mounts
    const storedAnswers = loadAnswersFromStorage();
    if (storedAnswers && Object.keys(storedAnswers).length > 0) {
      setAnswers(storedAnswers);
    }
  }, []); // Empty dependency array - only run on mount

  // Scroll to top when page changes
  useEffect(() => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, [currentPage]);

  // Reset to first category when assessment step changes (for better UX)
  useEffect(() => {
    setCurrentPage(0);
  }, [currentStep]);

  // Auto-fill answers in debug mode (only once when debug mode is enabled)
  useEffect(() => {
    if (isDebugMode && import.meta.env.DEV) {
      // Check if we already have answers (from localStorage or previous state)
      const currentAnswerCount = Object.keys(answers).length;

      // Only auto-fill if we don't have answers already
      if (currentAnswerCount === 0) {
        const autoAnswers = {};

        // Generate random answers for all questions (5-7 range for good scores)
        allQuestions.forEach((q) => {
          autoAnswers[q.questionKey] = Math.floor(Math.random() * 3) + 5; // Random 5-7
        });

        setAnswers(autoAnswers);
        saveAnswersToStorage(autoAnswers);
        setCurrentPage(Math.max(0, totalPages - 1)); // Go to last category
      }
    }
  }, [isDebugMode]); // Only depend on isDebugMode, not assessmentData.title

  // Handle prefilled answers from auto-fill all assessments (only when they change)
  useEffect(() => {
    if (prefilledAnswers && Object.keys(prefilledAnswers).length > 0) {
      // Filter answers for current assessment
      const currentAssessmentAnswers = {};
      allQuestions.forEach((q) => {
        if (prefilledAnswers[q.questionKey] !== undefined) {
          currentAssessmentAnswers[q.questionKey] = prefilledAnswers[q.questionKey];
        }
      });

      if (Object.keys(currentAssessmentAnswers).length > 0) {
        setAnswers(currentAssessmentAnswers);
        saveAnswersToStorage(currentAssessmentAnswers);

        // Navigate to last category if all assessments are filled and we're in the last phase
        if (allAssessmentsFilled && currentStep === 3) {
          setCurrentPage(Math.max(0, totalPages - 1));
        }
      }
    }
  }, [prefilledAnswers, allAssessmentsFilled]); // Depend on prefilledAnswers and allAssessmentsFilled

  const handleAnswerChange = (questionKey, value) => {
    const newAnswers = {
      ...answers,
      [questionKey]: value
    };
    setAnswers(newAnswers);

    // Save to localStorage after state update
    saveAnswersToStorage(newAnswers);
  };

  const handleNextCategory = () => {
    if (currentPage < totalPages - 1) {
      setCurrentPage(prev => prev + 1);
    }
  };

  const handlePreviousCategory = () => {
    if (currentPage > 0) {
      setCurrentPage(prev => prev - 1);
    }
  };

  const calculateScores = () => {
    const scores = {};
    
    Object.entries(assessmentData.categories).forEach(([categoryKey, category]) => {
      let totalScore = 0;
      let questionCount = 0;
      
      // Regular questions
      category.questions.forEach((_, index) => {
        const questionKey = `${categoryKey}_${index}`;
        if (answers[questionKey]) {
          totalScore += answers[questionKey];
          questionCount++;
        }
      });
      
      // Reverse questions (for Big Five)
      if (category.reverseQuestions) {
        category.reverseQuestions.forEach((_, index) => {
          const questionKey = `${categoryKey}_reverse_${index}`;
          if (answers[questionKey]) {
            // Reverse the score (8 - original score for 1-7 scale)
            totalScore += (8 - answers[questionKey]);
            questionCount++;
          }
        });
      }
      
      // Calculate average score (0-100 scale)
      if (questionCount > 0) {
        scores[categoryKey] = Math.round(((totalScore / questionCount) - 1) * (100 / 6)); // Convert 1-7 to 0-100
      }
    });
    
    return scores;
  };

  const handleSubmit = () => {
    const scores = calculateScores();
    onSubmit(scores);

    // Clear localStorage for current assessment when moving to next
    if (!isLastAssessment) {
      clearAnswersFromStorage();
      onNext();
    } else {
      // For last assessment, clear will be handled by parent component
      // after successful API submission
    }
  };

  // Clear localStorage when parent requests it (after successful submission)
  useEffect(() => {
    if (onClearStoredAnswers) {
      // Register this component's clear function with parent
      onClearStoredAnswers(clearAnswersFromStorage);
    }
  }, [onClearStoredAnswers]);

  const isAssessmentComplete = () => {
    return allQuestions.every(q => answers[q.questionKey] !== undefined);
  };

  // Function to find first unanswered category
  const findFirstUnansweredCategory = () => {
    for (let categoryIndex = 0; categoryIndex < categories.length; categoryIndex++) {
      const [categoryKey, categoryData] = categories[categoryIndex];

      // Check regular questions
      const hasUnansweredRegular = categoryData.questions.some((_, index) => {
        const questionKey = `${categoryKey}_${index}`;
        return answers[questionKey] === undefined;
      });

      if (hasUnansweredRegular) return categoryIndex;

      // Check reverse questions
      if (categoryData.reverseQuestions) {
        const hasUnansweredReverse = categoryData.reverseQuestions.some((_, index) => {
          const questionKey = `${categoryKey}_reverse_${index}`;
          return answers[questionKey] === undefined;
        });

        if (hasUnansweredReverse) return categoryIndex;
      }
    }
    return currentPage;
  };

  // Enhanced submit handler with validation
  const handleSubmitWithValidation = () => {
    if (!isAssessmentComplete()) {
      const firstUnansweredCategory = findFirstUnansweredCategory();
      setCurrentPage(firstUnansweredCategory);
      // You could also show a toast or alert here
      return;
    }
    handleSubmit();
  };

  // Function to fill all answers with random values
  const fillRandomAnswers = () => {
    const randomAnswers = {};

    // Generate random answers for all questions (1-7 range)
    allQuestions.forEach((q) => {
      randomAnswers[q.questionKey] = Math.floor(Math.random() * 7) + 1; // Random 1-7
    });

    setAnswers(randomAnswers);
  };

  return (
    <div className="min-h-screen bg-gray-50 mobile-container">
      {/* Mobile Navigation */}
      <MobileAssessmentNavbar
        assessmentData={assessmentData}
        currentStep={currentStep}
        totalSteps={totalSteps}
        answers={answers}
        onTogglePhaseMenu={() => setIsPhaseMenuOpen(true)}
      />

      {/* Mobile Phase Menu */}
      <MobilePhaseMenu
        currentStep={currentStep}
        onNavigateToPhase={onNavigateToPhase}
        onClose={() => setIsPhaseMenuOpen(false)}
        isOpen={isPhaseMenuOpen}
      />

      <div className="flex max-w-7xl mx-auto">
        {/* Main Content */}
        <div className="flex-1 lg:mr-80 p-2 sm:p-4 lg:p-8 pb-32 sm:pb-36 lg:pb-8">
          {/* Desktop Header */}
          <div className="hidden lg:block mb-8 bg-white border border-gray-200 p-8 max-w-3xl mx-auto">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="p-3 bg-gray-100 border border-gray-200">
                  <BookOpen className="h-6 w-6 text-gray-700" />
                </div>
                <div>
                  <h1 className="text-2xl font-semibold text-gray-900 tracking-tight">
                    {assessmentData.title}
                  </h1>
                  <p className="text-gray-600 text-sm mt-1 font-medium">
                    Assessment {currentStep} of {totalSteps} - {assessmentData.description}
                  </p>
                  {isAutoFillMode && (
                    <div className="mt-2 inline-flex items-center px-3 py-1 text-xs font-medium bg-gray-100 text-gray-700 border border-gray-300">
                      <Check className="h-3 w-3 mr-1" />
                      Auto-filled - You can edit answers manually
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>



          {/* Questions */}
          <div className="w-full max-w-full sm:max-w-2xl lg:max-w-3xl mx-auto px-1 sm:px-0">
            {currentQuestions.map((q, index) => {
              // Calculate the global question index for this question
              let globalIndex = 0;
              const categoriesArray = Object.entries(assessmentData.categories);

              // Count questions from previous categories
              for (let i = 0; i < currentPage; i++) {
                const [, categoryData] = categoriesArray[i];
                globalIndex += categoryData.questions.length;
                if (categoryData.reverseQuestions) {
                  globalIndex += categoryData.reverseQuestions.length;
                }
              }

              // Add current question index within current category
              globalIndex += index + 1;

              return (
                <AssessmentQuestion
                  key={q.questionKey}
                  question={q.question}
                  questionIndex={globalIndex}
                  totalQuestions={allQuestions.length}
                  scale={assessmentData.scale}
                  value={answers[q.questionKey]}
                  onChange={(value) => handleAnswerChange(q.questionKey, value)}
                  isReverse={q.isReverse}
                />
              );
            })}
          </div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex max-w-full sm:max-w-2xl lg:max-w-3xl mx-auto mt-8 justify-between items-center">
            <button
              onClick={handlePreviousCategory}
              disabled={currentPage === 0}
              className="flex items-center space-x-2 px-6 py-3 text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <ChevronLeft className="h-5 w-5" />
              <span>Previous Category</span>
            </button>

            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600 font-medium">
                {currentCategoryData?.name} ({currentPage + 1} of {totalPages})
              </span>

              {/* Previous Assessment Button */}
              {currentStep > 1 && currentPage === 0 && (
                <button
                  onClick={onPrevious}
                  className="flex items-center space-x-2 px-6 py-3 text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 transition-colors"
                >
                  <ChevronLeft className="h-5 w-5" />
                  <span>Previous Assessment</span>
                </button>
              )}

              {/* Submit Assessment Button */}
              {currentPage === totalPages - 1 && isLastAssessment && !isAutoFillMode && (
                <button
                  onClick={handleSubmitWithValidation}
                  disabled={!isAssessmentComplete() || isProcessingSubmit}
                  className="flex items-center space-x-2 px-6 py-3 bg-gray-900 text-white hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
                >
                  <Check className="h-5 w-5" />
                  <span>{isProcessingSubmit ? 'Submitting...' : 'Submit Assessment'}</span>
                </button>
              )}

              {/* Manual Submit Button for Auto-Fill Mode */}
              {isAutoFillMode && isLastAssessment && (
                <button
                  onClick={onManualSubmit}
                  disabled={isProcessingSubmit}
                  className="flex items-center space-x-2 px-6 py-3 bg-gray-900 text-white hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
                >
                  <Check className="h-5 w-5" />
                  <span>{isProcessingSubmit ? 'Submitting...' : 'Submit All Assessments'}</span>
                </button>
              )}

              {/* Next Assessment Button */}
              {currentPage === totalPages - 1 && !isLastAssessment && (
                <button
                  onClick={handleSubmitWithValidation}
                  disabled={!isAssessmentComplete() || isProcessingSubmit}
                  className="flex items-center space-x-2 px-6 py-3 bg-gray-900 text-white hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
                >
                  <span>{isProcessingSubmit ? 'Processing...' : 'Next Assessment'}</span>
                  <ChevronRight className="h-5 w-5" />
                </button>
              )}

              {/* Next Category Button */}
              {currentPage < totalPages - 1 && (
                <button
                  onClick={handleNextCategory}
                  className="flex items-center space-x-2 px-6 py-3 bg-gray-900 text-white hover:bg-gray-800 transition-all"
                >
                  <span>Next Category</span>
                  <ChevronRight className="h-5 w-5" />
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Desktop Sidebar */}
        <AssessmentSidebar
          assessmentData={assessmentData}
          answers={answers}
          currentPage={currentPage}
          setCurrentPage={setCurrentPage}
          currentStep={currentStep}
          totalSteps={totalSteps}
          onFillRandomAnswers={fillRandomAnswers}
          onFillAllAssessments={onFillAllAssessments}
          onNavigateToPhase={onNavigateToPhase}
        />
      </div>

      {/* Mobile Bottom Navigation */}
      <MobileBottomNavigation
        currentPage={currentPage}
        totalPages={totalPages}
        currentStep={currentStep}
        totalSteps={totalSteps}
        isLastAssessment={isLastAssessment}
        isAssessmentComplete={isAssessmentComplete}
        isProcessingSubmit={isProcessingSubmit}
        isAutoFillMode={isAutoFillMode}
        onPreviousCategory={handlePreviousCategory}
        onNextCategory={handleNextCategory}
        onPrevious={onPrevious}
        onSubmitWithValidation={handleSubmitWithValidation}
        onManualSubmit={onManualSubmit}
        currentCategoryData={currentCategoryData}
      />
    </div>
  );
};

export default AssessmentForm;
